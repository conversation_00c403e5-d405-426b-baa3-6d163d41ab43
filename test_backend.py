#!/usr/bin/env python3
"""
Test script to check if the WhatsApp bot backend is working properly.
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://127.0.0.1:5000"
WEBHOOK_URL = f"{BASE_URL}/webhook"

def test_webhook_verification():
    """Test the GET webhook verification endpoint"""
    print("🔍 Testing webhook verification...")
    
    params = {
        "hub.verify_token": "your_verify_token",
        "hub.challenge": "test_challenge_12345"
    }
    
    try:
        response = requests.get(WEBHOOK_URL, params=params, timeout=10)
        if response.status_code == 200 and response.text == "test_challenge_12345":
            print("✅ Webhook verification: PASSED")
            return True
        else:
            print(f"❌ Webhook verification: FAILED (Status: {response.status_code}, Response: {response.text})")
            return False
    except Exception as e:
        print(f"❌ Webhook verification: ERROR - {e}")
        return False

def test_webhook_post_basic():
    """Test basic POST webhook functionality"""
    print("🔍 Testing basic POST webhook...")
    
    # Simple test payload
    test_payload = {
        "entry": [{
            "changes": [{
                "value": {
                    "messages": [{
                        "type": "text",
                        "text": {"body": "Hello, test message"}
                    }],
                    "contacts": [{"wa_id": "1234567890"}]
                }
            }]
        }]
    }
    
    try:
        response = requests.post(
            WEBHOOK_URL, 
            json=test_payload, 
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        if response.status_code == 200:
            print("✅ Basic POST webhook: PASSED")
            return True
        else:
            print(f"❌ Basic POST webhook: FAILED (Status: {response.status_code})")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Basic POST webhook: ERROR - {e}")
        return False

def test_server_health():
    """Test if the server is running and responsive"""
    print("🔍 Testing server health...")
    
    try:
        # Try to connect to the base URL
        response = requests.get(BASE_URL, timeout=5)
        print("✅ Server is running and responsive")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server health: Cannot connect to server")
        return False
    except Exception as e:
        print(f"❌ Server health: ERROR - {e}")
        return False

def test_environment_variables():
    """Check if required environment variables are set"""
    print("🔍 Checking environment configuration...")
    
    # This is a basic check - we can't directly access env vars from the running app
    # but we can infer from the code structure
    print("ℹ️  Environment variables should be checked manually:")
    print("   - WHATSAPP_TOKEN")
    print("   - OPENAI_API_KEY") 
    print("   - GEMINI_API_KEY")
    print("✅ Environment check: Manual verification needed")
    return True

def main():
    """Run all tests"""
    print("🚀 Starting WhatsApp Bot Backend Tests")
    print("=" * 50)
    
    tests = [
        ("Server Health", test_server_health),
        ("Webhook Verification", test_webhook_verification),
        ("Basic POST Webhook", test_webhook_post_basic),
        ("Environment Variables", test_environment_variables)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Your backend appears to be working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
